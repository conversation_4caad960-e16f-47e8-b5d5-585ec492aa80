<?php

namespace Modules\Bank\Strategies;

use App\Settings\BankSettings;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Modules\Bank\Contracts\BankStrategyInterface;
use Modules\Bank\Integrations\Mb\MbConnector;
use Modules\Bank\Integrations\Mb\Requests\CheckAccountRequest;
use Modules\Bank\Integrations\Mb\Requests\CreateVirtualAccountRequest;
use Modules\Bank\Integrations\Mb\Requests\GenerateTokenRequest;
use Modules\Bank\Integrations\Mb\Requests\GetVirtualAccountTransactionsRequest;
use Modules\Bank\Integrations\Mb\Requests\RegisterAccountRequest;
use Modules\Bank\Integrations\Mb\Requests\SyncTransactionRequest;
use Modules\Bank\Integrations\Mb\Requests\SyncVirtualAccountTransactionRequest;
use Modules\Bank\Integrations\Mb\Requests\ValidateVirtualAccountRequest;

/**
 * MBBank Strategy Implementation
 *
 * Implements bank operations for MBBank using their API with RSA signature
 */
class MbStrategy implements BankStrategyInterface
{
    protected array $config;

    protected BankSettings $bankSettings;

    protected MbConnector $connector;

    public function __construct(array $config = [])
    {
        $this->config = $config;
        $this->bankSettings = app(BankSettings::class);
        $this->connector = new MbConnector;
    }

    /**
     * Check if account number is valid and get account information
     */
    public function checkAccountNumber(string $accountNumber, array $additionalData = []): array
    {
        try {
            $response = $this->connector->send(new CheckAccountRequest($accountNumber));

            if ($response->failed()) {
                Log::warning('MBBank account validation failed', [
                    'account_number' => $accountNumber,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình kết nối với ngân hàng MBBank.',
                    'error_code' => 'API_ERROR',
                ];
            }

            $responseData = $response->json();

            // Verify response signature
            if (! $this->verifyResponseSignature($responseData)) {
                Log::warning('MBBank response signature verification failed', [
                    'account_number' => $accountNumber,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => false,
                    'message' => 'Phản hồi từ ngân hàng MBBank không hợp lệ.',
                    'error_code' => 'SIGNATURE_ERROR',
                ];
            }

            $data = $responseData['data'] ?? [];
            $responseCode = $data['responseCode'] ?? '01';

            if ($responseCode === '00') {
                Log::info('MBBank account validation successful', [
                    'account_number' => $accountNumber,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => true,
                    'message' => 'Thông tin tài khoản MBBank hợp lệ.',
                    'account_name' => $data['customerName'] ?? null,
                    'account_number' => $accountNumber,
                    'data' => $responseData,
                ];
            }

            return [
                'success' => false,
                'message' => $data['responseDesc'] ?? 'Tài khoản không hợp lệ.',
                'error_code' => 'INVALID_ACCOUNT',
            ];

        } catch (Exception $e) {
            Log::error('MBBank account validation error', [
                'account_number' => $accountNumber,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình kiểm tra tài khoản MBBank.',
                'error_code' => 'INTERNAL_ERROR',
            ];
        }
    }

    /**
     * Register account for banking services
     */
    public function registerAccount(array $accountData): array
    {
        try {
            $response = $this->connector->send(new RegisterAccountRequest($accountData));

            if ($response->failed()) {
                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình đăng ký tài khoản MBBank.',
                    'error_code' => 'API_ERROR',
                ];
            }

            $responseData = $response->json();

            return [
                'success' => true,
                'message' => 'Đăng ký tài khoản MBBank thành công.',
                'data' => $responseData,
                'access_token' => $responseData['access_token'] ?? null,
            ];

        } catch (Exception $e) {
            Log::error('MBBank registration error', [
                'account_data' => $accountData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đăng ký tài khoản MBBank.',
                'error_code' => 'INTERNAL_ERROR',
            ];
        }
    }

    /**
     * Get transaction history for an account
     */
    public function getTransactionHistory(string $accountNumber, array $filters = []): array
    {
        Log::info('MBBank transaction history requested', [
            'account_number' => $accountNumber,
            'filters' => $filters,
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng lịch sử giao dịch MBBank đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED',
            'transactions' => [],
            'total_count' => 0,
        ];
    }

    /**
     * Create virtual account (if supported by bank)
     */
    public function createVirtualAccount(array $virtualAccountData): array
    {
        try {
            $response = $this->connector->send(new CreateVirtualAccountRequest($virtualAccountData));

            if ($response->failed()) {
                Log::error('MBBank virtual account creation failed', [
                    'virtual_account_data' => $virtualAccountData,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình tạo tài khoản ảo MBBank.',
                    'error_code' => 'API_ERROR',
                ];
            }

            $responseData = $response->json();

            // Verify response signature
            if (! $this->verifyResponseSignature($responseData)) {
                Log::warning('MBBank VA creation response signature verification failed', [
                    'virtual_account_data' => $virtualAccountData,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => false,
                    'message' => 'Phản hồi từ ngân hàng MBBank không hợp lệ.',
                    'error_code' => 'SIGNATURE_ERROR',
                ];
            }

            $data = $responseData['data'] ?? [];
            $responseCode = $data['responseCode'] ?? '01';

            if ($responseCode === '00') {
                Log::info('MBBank virtual account created successfully', [
                    'virtual_account_data' => $virtualAccountData,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => true,
                    'message' => 'Tạo tài khoản ảo MBBank thành công.',
                    'data' => $responseData,
                ];
            }

            return [
                'success' => false,
                'message' => $data['responseDesc'] ?? 'Không thể tạo tài khoản ảo.',
                'error_code' => 'CREATION_FAILED',
            ];

        } catch (Exception $e) {
            Log::error('MBBank virtual account creation error', [
                'virtual_account_data' => $virtualAccountData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình tạo tài khoản ảo MBBank.',
                'error_code' => 'INTERNAL_ERROR',
            ];
        }
    }

    /**
     * Delete/disable virtual account (if supported by bank)
     */
    public function deleteVirtualAccount(array $virtualAccountData): array
    {
        Log::info('MBBank virtual account deletion requested', [
            'virtual_account_data' => $virtualAccountData,
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng xóa tài khoản ảo MBBank đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED',
        ];
    }

    /**
     * Get virtual account transactions
     */
    public function getVirtualAccountTransactions(array $filters = []): array
    {
        try {
            $response = $this->connector->send(new GetVirtualAccountTransactionsRequest($filters));

            if ($response->failed()) {
                Log::error('MBBank virtual account transactions failed', [
                    'filters' => $filters,
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình lấy giao dịch tài khoản ảo MBBank.',
                    'error_code' => 'API_ERROR',
                    'transactions' => [],
                ];
            }

            $responseData = $response->json();

            // Verify response signature
            if (! $this->verifyTransactionResponseSignature($responseData)) {
                Log::warning('MBBank transaction response signature verification failed', [
                    'filters' => $filters,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => false,
                    'message' => 'Phản hồi giao dịch từ ngân hàng MBBank không hợp lệ.',
                    'error_code' => 'SIGNATURE_ERROR',
                    'transactions' => [],
                ];
            }

            $data = $responseData['data'] ?? [];
            $responseCode = $data['responseCode'] ?? '01';

            if ($responseCode === '00') {
                Log::info('MBBank virtual account transactions retrieved successfully', [
                    'filters' => $filters,
                    'response_data' => $responseData,
                ]);

                return [
                    'success' => true,
                    'message' => 'Lấy giao dịch tài khoản ảo MBBank thành công.',
                    'transactions' => [$data], // MBBank returns single transaction
                    'data' => $responseData,
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $data['responseDesc'] ?? 'Không thể lấy giao dịch.',
                    'error_code' => 'TRANSACTION_FAILED',
                    'transactions' => [],
                ];
            }

        } catch (Exception $e) {
            Log::error('MBBank virtual account transactions error', [
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình lấy giao dịch tài khoản ảo MBBank.',
                'error_code' => 'INTERNAL_ERROR',
                'transactions' => [],
            ];
        }
    }

    /**
     * Link account to banking services
     */
    public function linkAccount(string $accountNumber): array
    {
        Log::info('MBBank account linking requested', [
            'account_number' => $accountNumber,
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng liên kết tài khoản MBBank đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED',
        ];
    }

    /**
     * Verify linked account with OTP
     */
    public function verifyLinkedAccount(array $verificationData): array
    {
        Log::info('MBBank account verification requested', [
            'verification_data' => $verificationData,
        ]);

        return [
            'success' => false,
            'message' => 'Tính năng xác thực tài khoản MBBank đang được phát triển.',
            'error_code' => 'NOT_IMPLEMENTED',
        ];
    }

    /**
     * Get account balance (if supported by bank)
     */
    public function getAccountBalance(string $accountNumber): ?float
    {
        Log::info('MBBank balance check requested', [
            'account_number' => $accountNumber,
        ]);

        // MBBank supports balance check according to config, but not implemented yet
        return null;
    }

    /**
     * Check if bank supports specific feature
     */
    public function supportsFeature(string $feature): bool
    {
        $supportedFeatures = $this->config['features'] ?? [];

        return in_array($feature, $supportedFeatures);
    }

    /**
     * Get bank configuration
     */
    public function getBankConfig(): array
    {
        return $this->config;
    }

    /**
     * Create RSA signature for data
     */
    protected function createSignature(string $data): string
    {
        $privateKey = config('bank.supported_banks.mb.api_config.autopay_private_key');
        openssl_sign($data, $signature, $privateKey, OPENSSL_ALGO_SHA256);

        return base64_encode($signature);
    }

    /**
     * Verify RSA signature from MBBank
     */
    protected function verifySignature(string $data, string $signature): bool
    {
        $publicKey = config('bank.supported_banks.mb.api_config.mb_public_key');
        $verify = openssl_verify($data, base64_decode($signature), $publicKey, OPENSSL_ALGO_SHA256);

        return $verify === 1;
    }

    /**
     * Verify response signature from MBBank VA account endpoint
     */
    protected function verifyResponseSignature(array $responseData): bool
    {
        if (! isset($responseData['signature']) || ! isset($responseData['data']['customerAcc'])) {
            return false;
        }

        return $this->verifySignature(
            $responseData['data']['customerAcc'],
            $responseData['signature']
        );
    }

    /**
     * Verify transaction response signature from MBBank
     */
    protected function verifyTransactionResponseSignature(array $responseData): bool
    {
        if (! isset($responseData['signature']) || ! isset($responseData['data'])) {
            return false;
        }

        $data = $responseData['data'];
        $signatureData = ($data['transactionId'] ?? '').($data['responseCode'] ?? '');

        return $this->verifySignature($signatureData, $responseData['signature']);
    }

    /**
     * Sync transaction with MBBank
     */
    public function syncTransaction(array $transactionData): array
    {
        try {
            $response = $this->connector->send(new SyncTransactionRequest($transactionData));

            if ($response->failed()) {
                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình đồng bộ giao dịch MBBank.',
                    'error_code' => 'API_ERROR',
                ];
            }

            $responseData = $response->json();

            return [
                'success' => true,
                'message' => 'Đồng bộ giao dịch MBBank thành công.',
                'data' => $responseData,
            ];

        } catch (Exception $e) {
            Log::error('MBBank transaction sync error', [
                'transaction_data' => $transactionData,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đồng bộ giao dịch MBBank.',
                'error_code' => 'INTERNAL_ERROR',
            ];
        }
    }

    /**
     * Authenticate with MBBank and get access token
     */
    public function authenticate(): ?string
    {
        try {
            $credentials = config('bank.supported_banks.mb.api_config');

            if (! $credentials['auth_token']) {
                return null;
            }

            // MBBank uses static auth token from config
            $token = $credentials['auth_token'];

            if ($token) {
                // Save token to BankSettings with long expiration (MBBank tokens are typically long-lived)
                $expiresAt = now()->addDays(30)->toDateTimeString();
                $this->bankSettings->setAccessToken('mb', $token, $expiresAt);

                return $token;
            }

            return null;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Refresh access token using refresh token
     */
    public function refreshToken(): ?string
    {
        // MBBank doesn't support refresh tokens, re-authenticate instead
        Log::info('MBBank refresh token requested, re-authenticating instead');

        return $this->authenticate();
    }

    /**
     * Get valid access token (authenticate if needed)
     */
    public function getAccessToken(): ?string
    {
        $token = $this->bankSettings->getAccessToken('mb');

        if (! $token || $this->bankSettings->isTokenExpired('mb')) {
            $token = $this->authenticate();
        }

        return $token;
    }

    /**
     * Make authenticated HTTP request to bank API
     */
    public function makeAuthenticatedRequest(string $method, string $endpoint, array $data = []): array
    {
        try {
            // For MBBank, we use specific request classes instead of generic requests
            // This method is kept for interface compatibility but delegates to specific methods
            Log::info('MBBank makeAuthenticatedRequest called', [
                'method' => $method,
                'endpoint' => $endpoint,
                'data' => $data,
            ]);

            return [
                'success' => true,
                'message' => 'MBBank uses specific request classes. Please use appropriate methods.',
                'data' => [],
            ];
        } catch (Exception $e) {
            Log::error('Error making authenticated request to MBBank', [
                'error' => $e->getMessage(),
                'endpoint' => $endpoint,
            ]);

            return [
                'success' => false,
                'message' => 'Request failed due to network error',
                'error_code' => 'NETWORK_ERROR',
            ];
        }
    }

    /**
     * Test bank connection
     */
    public function testConnection(): array
    {
        try {
            $token = $this->getAccessToken();

            if (! $token) {
                return [
                    'success' => false,
                    'message' => 'Unable to authenticate with MBBank',
                    'error_code' => 'AUTH_FAILED',
                ];
            }

            return [
                'success' => true,
                'message' => 'MBBank connection successful',
                'token_expires_at' => $this->bankSettings->getTokenExpiration('mb'),
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'MBBank connection test failed: '.$e->getMessage(),
                'error_code' => 'CONNECTION_FAILED',
            ];
        }
    }

    /**
     * Clear all tokens for MBBank
     */
    public function clearTokens(): void
    {
        $this->bankSettings->clearTokens('mb');
        Log::info('Cleared all tokens for MBBank');
    }

    /**
     * Get token status for MBBank
     */
    public function getTokenStatus(): array
    {
        $token = $this->bankSettings->getAccessToken('mb');
        $expiresAt = $this->bankSettings->getTokenExpiration('mb');
        $requestedAt = $this->bankSettings->getTokenRequestedAt('mb');
        $isExpired = $this->bankSettings->isTokenExpired('mb');

        return [
            'has_token' => ! empty($token),
            'expires_at' => $expiresAt,
            'requested_at' => $requestedAt,
            'is_expired' => $isExpired,
            'expires_in_minutes' => $expiresAt ? Carbon::parse($expiresAt)->diffInMinutes(now()) : null,
            'age_in_minutes' => $requestedAt ? Carbon::parse($requestedAt)->diffInMinutes(now()) : null,
        ];
    }

    /**
     * Generate access token
     */
    public function generateToken(): array
    {
        try {
            $response = $this->connector->send(new GenerateTokenRequest);

            if ($response->failed()) {
                Log::error('MBBank token generation failed', [
                    'response_status' => $response->status(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra trong quá trình tạo token MBBank.',
                    'error_code' => 'TOKEN_ERROR',
                ];
            }

            $responseData = $response->json();

            Log::info('MBBank token generation successful', [
                'response_data' => $responseData,
            ]);

            return [
                'success' => true,
                'access_token' => $responseData['access_token'] ?? config('bank.supported_banks.mb.api_config.auth_token'),
                'token_type' => 'bearer',
                'expires_in' => $responseData['expires_in'] ?? 59,
            ];

        } catch (Exception $e) {
            Log::error('MBBank token generation error', [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình tạo token MBBank.',
                'error_code' => 'TOKEN_ERROR',
            ];
        }
    }

    /**
     * Validate virtual account
     */
    public function validateVirtualAccount(string $customerAcc): array
    {
        $request = new ValidateVirtualAccountRequest($customerAcc);

        return $request->validateAccount();
    }

    /**
     * Sync virtual account transaction
     */
    public function syncVirtualAccountTransaction(array $transactionData): array
    {
        $request = new SyncVirtualAccountTransactionRequest($transactionData);

        return $request->syncTransaction();
    }

    /**
     * Create transaction signature
     */
    protected function createTransactionSignature(string $data): string
    {
        $privateKey = config('bank.supported_banks.mb.api_config.autopay_private_key');
        openssl_sign($data, $signature, $privateKey, OPENSSL_ALGO_SHA256);

        return base64_encode($signature);
    }
}
